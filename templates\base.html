<!DOCTYPE html>
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
<html lang="he" dir="rtl">
  <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <title>{% block title %}Israeli Coffee{% endblock %}</title>
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'varela': ['"Varela Round"', 'sans-serif'],
            'rammetto': ['"Rammetto One"', 'cursive'],
            'noto': ['"Noto Sans Hebrew"', 'Arial', 'sans-serif'],
          }
        }
      }
    }
  </script>
  <link href="https://fonts.googleapis.com/css2?family=Varela+Round&display=swap" rel="stylesheet">
  <!-- Add the Rammetto One font -->
  <link href="https://fonts.googleapis.com/css2?family=Rammetto+One&display=swap" rel="stylesheet">
  <!-- Add Font Awesome for the accessibility icon -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <!-- Replace David Libre with Noto Sans Hebrew font for the readable font option -->
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Hebrew:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Include our responsive CSS -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.css') }}">
  <!-- ...other head elements... -->
  <style>
    body {
      font-family: 'Varela Round', sans-serif;
      transition: font-size 0.3s ease, filter 0.3s ease, background-color 0.3s ease, color 0.3s ease;
    }

    /* Minimal header styling with responsive design - reduced height by 50% */
    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: hsl(130, 72%, 54%);
      padding: 2px 10px; /* Reduced vertical padding */
      border-bottom: 1px solid #ccc;
      flex-wrap: wrap;
      max-height: 50px; /* Set maximum height */
      line-height: 1; /* Reduce line height */
    }

    #user-name {
      font-family: 'Varela Round', sans-serif;
      font-weight: bold;
      color: #333;
    }

    /* Modal container styles */
    #loginModal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4); /* semi-transparent overlay */
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }

    #loginModal .modal-content {
      background: #fff;  /* white popup */
      padding: 20px;
      border-radius: 8px;
      max-width: 400px;
      width: 90%;
    }

    .close-modal {
      float: left;
      cursor: pointer;
      font-size: 20px;
      color: red;
    }

    /* Accessibility Widget Styles - made touch-friendly */
    #accessibility-widget {
      position: fixed;
      bottom: 20px;
      left: 10px;
      width: 48px; /* Increased for touch */
      height: 48px; /* Increased for touch */
      background-color: #2196F3;
      color: white;
      border-radius: 70%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0,0,0,0.3);
      z-index: 999;
      transition: transform 0.3s ease;
    }

    /* Remove hover effect for touch devices */
    @media (hover: hover) {
      #accessibility-widget:hover {
        transform: scale(1.1);
      }
    }

    /* Increase the icon size by 100% */
    #accessibility-widget i {
      font-size: 200%; /* Double the original size */
    }

    #accessibility-modal {
      display: none;
      position: fixed !important; /* Force fixed positioning */
      bottom: 70px !important; /* Force bottom positioning */
      left: 20px !important; /* Force left positioning */
      right: auto !important; /* Prevent right-side positioning */
      top: auto !important; /* Prevent top positioning */
      margin: 0 !important; /* Remove any margins */
      transform: none !important; /* Prevent any transform */
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      padding: 10px; /* Reduced padding by ~30% from 15px */
      z-index: 1000;
      width: 175px; /* Reduced by 30% from 250px */
      direction: rtl;
      text-align: right;
    }

    #accessibility-modal h3 {
      margin-top: 0;
      margin-bottom: 10px; /* Reduced by ~30% from 15px */
      font-size: 14px; /* Reduced from 16px */
      font-weight: bold;
    }

    /* Updated accessibility option styles for full-width buttons */
    .accessibility-option {
      display: flex;
      flex-direction: column;
      gap: 7px; /* Reduced by 30% from 10px */
    }

    /* Updated button styles for full-width buttons - made touch-friendly */
    .accessibility-button {
      background-color: #f0f0f0;
      border: none;
      width: 100%;
      padding: 8px 11px; /* Reduced by ~30% from 12px 16px */
      border-radius: 4px;
      cursor: pointer;
      text-align: center;
      font-weight: bold;
      transition: background-color 0.2s;
      min-height: 31px; /* Reduced by ~30% from 44px */
      font-size: 12px; /* Added smaller font size */
    }

    /* Remove hover effect for touch devices */
    @media (hover: hover) {
      .accessibility-button:hover {
        background-color: #e0e0e0;
      }
    }

    .accessibility-close {
      position: absolute;
      top: 3px;
      left: 7px;
      cursor: pointer;
      font-size: 18px; /* Reduced by ~30% from 24px */
      color: #666;
      padding: 6px; /* Reduced by ~30% from 8px */
      min-width: 31px; /* Reduced by ~30% from 44px */
      min-height: 31px; /* Reduced by ~30% from 44px */
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Remove hover effect for touch devices */
    @media (hover: hover) {
      .accessibility-close:hover {
        color: #000;
      }
    }

    /* Grayscale style when applied */
    body.grayscale {
      filter: grayscale(100%);
    }

    /* High contrast mode */
    body.high-contrast {
      background-color: black !important;
      color: white !important;
    }

    /* Negative contrast mode */
    body.negative-contrast {
      filter: invert(100%);
    }

    /* Light background */
    body.light-background {
      background-color: #fff !important;
      color: #000 !important;
    }

    body.light-background button,
    body.light-background .custom-btn {
      background-color: #eee !important;
      color: #000 !important;
      border: 1px solid #000 !important;
    }

    /* Highlight links */
    body.highlight-links a {
      text-decoration: underline !important;
      font-weight: bold !important;
    }

    /* Readable font - updated to use Noto Sans Hebrew */
    body.readable-font,
    body.readable-font * {
      font-family: 'Noto Sans Hebrew', 'Arial', sans-serif !important;
    }

    /* Universal high-contrast for showBrew pages */
    body.showbrew.high-contrast * {
      background-color: black !important;
      color: white !important;
      border-color: white !important;
    }

    /* Universal light background for showBrew pages */
    body.showbrew.light-background * {
      background-color: #fff !important;
      color: #000 !important;
      border-color: #000 !important;
    }

    /* Media queries for responsive design */
    @media (max-width: 640px) {
      header {
        padding: 0px 10px; /* Adjusted padding to match button height */
        max-height: 36px; /* Exactly match the button height */
        min-height: 36px; /* Ensure a minimum height equal to the buttons */
        height: 36px; /* Fixed height to match buttons */
        display: flex;
        align-items: center;
        flex-direction: row !important; /* Force row layout */
        flex-wrap: nowrap !important; /* Prevent wrapping */
        justify-content: space-between !important; /* Space between items */
      }

      header > div {
        flex: 0 0 auto !important; /* Prevent growing or shrinking */
      }

      #user-name-container {
        width: auto !important; /* Override the w-full class in mobile */
        margin-bottom: 0 !important; /* Remove bottom margin */
      }

      #auth-header {
        width: auto !important; /* Allow natural width */
        margin-top: 0 !important; /* Remove any top margin */
        display: flex !important;
        align-items: center !important;
      }

      #homeBtn, #loginBtn, #logoutBtn {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        height: 36px !important; /* Ensure exact height */
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      .content {
        padding: 10px;
      }
    }
  </style>
</head>
<body>
  <header class="w-full flex flex-wrap justify-between items-center p-1 md:p-2">
    <!-- Display logged-in user name on left if available -->
    <div id="user-name-container" class="w-full md:w-auto mb-1 md:mb-0 flex items-center">
      {% if g.user %}
        <span id="user-name" class="text-sm">{{ g.user.display_name }}</span>
      {% endif %}
    </div>
    <!-- Authentication buttons on right -->
    <div id="auth-header" class="w-full md:w-auto flex justify-end items-center flex-nowrap">
      <button id="homeBtn" class="py-1 px-2 mr-2 sm:mr-4 min-w-[70px] sm:min-w-[80px] min-h-[36px] text-center rounded text-sm bg-gray-200">מסך ראשי</button>
      <!-- Add Show Brew button when logged in -->
      <!-- {% if g.user %}
      <button id="brewLogBtn" class="py-1 px-3 mr-2 min-w-[80px] min-h-[36px] text-center rounded text-sm bg-gray-200">החליטות שלי</button>
      {% endif %} -->
      {% if g.user %}
        <!-- <button id="editPriceBtn" class="py-1 px-2 mr-2 min-w-[70px] sm:min-w-[80px] min-h-[36px] text-center rounded text-sm bg-gray-200">עריכת מחירים</button> -->
        <button id="logoutBtn" class="py-1 px-2 min-w-[70px] sm:min-w-[80px] min-h-[36px] text-center rounded text-sm bg-gray-200">התנתקות</button>
      {% else %}
        <button id="loginBtn" class="py-1 px-2 min-w-[70px] sm:min-w-[80px] min-h-[36px] text-center rounded text-sm bg-gray-200">התחברות</button>
      {% endif %}
    </div>
  </header>

  <!-- Page content -->
  <div class="content">
    {% block content %}
    <!-- Page-specific content goes here -->
    {% endblock %}
  </div>

  <!-- Include the login modal HTML -->
  {% include 'login_modal.html' %}

  <!-- Accessibility Widget -->
  <div id="accessibility-widget" title="אפשרויות נגישות">
    <i class="fas fa-universal-access"></i>
  </div>

  <!-- Accessibility Modal -->
  <div id="accessibility-modal">
    <span class="accessibility-close" title="סגור">&times;</span>
    <h3>אפשרויות נגישות</h3>
    <div class="accessibility-option">
      <button class="accessibility-button" id="increase-font" title="הגדל גופן">הגדלת טקסט</button>
      <button class="accessibility-button" id="decrease-font" title="הקטן גופן">הקטנת טקסט</button>
      <button class="accessibility-button" id="toggle-grayscale" title="מעבר לגווני אפור">גווני אפור</button>
      <button class="accessibility-button" id="toggle-high-contrast" title="ניגודיות גבוהה">ניגודיות גבוהה</button>
      <button class="accessibility-button" id="toggle-negative-contrast" title="ניגודיות הפוכה">ניגודיות הפוכה</button>
      <button class="accessibility-button" id="toggle-light-background" title="רקע בהיר">רקע בהיר</button>
      <button class="accessibility-button" id="toggle-highlight-links" title="הדגשת קישורים">הדגשת קישורים</button>
      <button class="accessibility-button" id="toggle-readable-font" title="פונט קריא">פונט קריא</button>
      <!-- Add reset button - styled differently to stand out -->
      <button class="accessibility-button" id="reset-accessibility" title="איפוס כל האפשרויות"
              style="margin-top: 10px; background-color: #ff6b6b; color: white;">איפוס</button>
    </div>
  </div>

  <!-- Firebase SDKs (if still used for sign-in) -->
  <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Fetch Firebase configuration from server
      fetch('/get_firebase_config')
        .then(response => response.json())
        .then(firebaseConfig => {
          if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
          }
          const auth = firebase.auth();

      // Get elements after DOM has loaded
      const loginBtn = document.getElementById('loginBtn');
      const logoutBtn = document.getElementById('logoutBtn');
      const loginModal = document.getElementById('loginModal');
      const closeModalBtn = document.getElementById('closeLoginModal');

      // Login modal event listeners
      if (loginBtn) {
        loginBtn.addEventListener('click', () => {
          loginModal.style.display = 'flex';
        });
      }
      if (closeModalBtn) {
        closeModalBtn.addEventListener('click', () => {
          loginModal.style.display = 'none';
        });
      }
      window.addEventListener('click', (e) => {
        if (e.target === loginModal) {
          loginModal.style.display = 'none';
        }
      });

      // Attach logout handler only if logoutBtn exists
      if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
          // Redirect directly to the logout endpoint to clear session
          window.location.href = '/logout';
        });
      }

      // Check server-side session to adjust button visibility
      fetch('/check_session', {
        method: 'GET',
        credentials: 'same-origin'
      })
        .then(response => response.json())
        .then(data => {
          if (data.authenticated) {
            if (loginBtn) loginBtn.style.display = 'none';
            if (logoutBtn) logoutBtn.style.display = 'block';
          } else {
            if (loginBtn) loginBtn.style.display = 'block';
            if (logoutBtn) logoutBtn.style.display = 'none';
          }
        })
        .catch(error => {
          if (loginBtn) loginBtn.style.display = 'block';
          if (logoutBtn) logoutBtn.style.display = 'none';
        });

      // Listen for Firebase auth state changes as a backup
      auth.onAuthStateChanged(user => {
        // Only check session if we have a valid user or if we're explicitly logged out
        // This prevents unnecessary API calls when user is not authenticated
        if (user || document.getElementById('logoutBtn')?.style.display !== 'none') {
          fetch('/check_session')
            .then(response => response.json())
            .then(data => {
              const serverAuthenticated = data.authenticated;
              const firebaseAuthenticated = !!user;
              if (serverAuthenticated !== firebaseAuthenticated) {
                if (serverAuthenticated) {
                  if (loginBtn) loginBtn.style.display = 'none';
                  if (logoutBtn) logoutBtn.style.display = 'block';
                } else {
                  if (loginBtn) loginBtn.style.display = 'block';
                  if (logoutBtn) logoutBtn.style.display = 'none';
                }
              }
            })
            .catch(error => {
              // Silent error handling for auth state changes
            });
        } else {
          // User is not authenticated, ensure login button is visible
          if (loginBtn) loginBtn.style.display = 'block';
          if (logoutBtn) logoutBtn.style.display = 'none';
        }
      });
    })
    .catch(error => {
      // Silent error handling for Firebase configuration
    });

      // Add navigation button event listeners
      const homeBtn = document.getElementById('homeBtn');
      if (homeBtn) {
        homeBtn.addEventListener('click', function() {
          window.location.href = "{{ url_for('home') }}";
        });
      }

      const editPriceBtn = document.getElementById('editPriceBtn');
      if (editPriceBtn) {
        editPriceBtn.addEventListener('click', function() {
          window.location.href = "{{ url_for('edit_price') }}";
        });
      }

      const brewLogBtn = document.getElementById('brewLogBtn');
      if (brewLogBtn) {
        brewLogBtn.addEventListener('click', function() {
          window.location.href = "{{ url_for('show_brew') }}";
        });
      }

      // Accessibility Widget JavaScript
      let currentFontSize = 1.0;
      const originalFontSize = parseFloat(window.getComputedStyle(document.body).fontSize);
      const accessibilityWidget = document.getElementById('accessibility-widget');
      const accessibilityIcon = accessibilityWidget.querySelector('i'); // Get the icon element
      const accessibilityModal = document.getElementById('accessibility-modal');
      const closeButton = document.querySelector('.accessibility-close');
      const increaseButton = document.getElementById('increase-font');
      const decreaseButton = document.getElementById('decrease-font');
      const grayscaleButton = document.getElementById('toggle-grayscale');
      const highContrastButton = document.getElementById('toggle-high-contrast');
      const negativeContrastButton = document.getElementById('toggle-negative-contrast');
      const lightBackgroundButton = document.getElementById('toggle-light-background');
      const highlightLinksButton = document.getElementById('toggle-highlight-links');
      const readableFontButton = document.getElementById('toggle-readable-font');
      const resetButton = document.getElementById('reset-accessibility');

      // Check if grayscale was enabled in a previous session
      if (sessionStorage.getItem('grayscaleEnabled') === 'true') {
        document.body.classList.add('grayscale');
      }

      if (sessionStorage.getItem('highContrastEnabled') === 'true') {
        document.body.classList.add('high-contrast');
      }

      if (sessionStorage.getItem('negativeContrastEnabled') === 'true') {
        document.body.classList.add('negative-contrast');
      }

      if (sessionStorage.getItem('lightBackgroundEnabled') === 'true') {
        document.body.classList.add('light-background');
      }

      if (sessionStorage.getItem('highlightLinksEnabled') === 'true') {
        document.body.classList.add('highlight-links');
      }

      if (sessionStorage.getItem('readableFontEnabled') === 'true') {
        document.body.classList.add('readable-font');
      }

      // Update the widget click event to handle clicks on the icon too
      function toggleAccessibilityModal() {
        if (accessibilityModal.style.display === 'block') {
          accessibilityModal.style.display = 'none';
        } else {
          accessibilityModal.style.display = 'block';
        }
      }

      // Add event listener to the widget
      accessibilityWidget.addEventListener('click', toggleAccessibilityModal);

      // Ensure clicks on the icon also trigger the modal
      if (accessibilityIcon) {
        accessibilityIcon.addEventListener('click', function(e) {
          e.stopPropagation(); // Prevent event bubbling
          toggleAccessibilityModal();
        });
      }

      closeButton.addEventListener('click', function() {
        accessibilityModal.style.display = 'none';
      });

      increaseButton.addEventListener('click', function() {
        if (currentFontSize < 1.5) {
          currentFontSize += 0.1;
          document.body.style.fontSize = (currentFontSize * 100) + '%';
          sessionStorage.setItem('accessibilityFontSize', currentFontSize);
        }
      });

      decreaseButton.addEventListener('click', function() {
        if (currentFontSize > 0.8) {
          currentFontSize -= 0.1;
          document.body.style.fontSize = (currentFontSize * 100) + '%';
          sessionStorage.setItem('accessibilityFontSize', currentFontSize);
        }
      });

      if (sessionStorage.getItem('accessibilityFontSize')) {
        currentFontSize = parseFloat(sessionStorage.getItem('accessibilityFontSize'));
        document.body.style.fontSize = (currentFontSize * 100) + '%';
      }

      // Add grayscale toggle functionality
      grayscaleButton.addEventListener('click', function() {
        if (document.body.classList.contains('grayscale')) {
          document.body.classList.remove('grayscale');
          sessionStorage.setItem('grayscaleEnabled', 'false');
          this.textContent = 'גווני אפור';
        } else {
          document.body.classList.add('grayscale');
          sessionStorage.setItem('grayscaleEnabled', 'true');
          this.textContent = 'צבעים רגילים';
        }
      });

      // Toggle high contrast mode
      highContrastButton.addEventListener('click', function() {
        document.body.classList.toggle('high-contrast');
        const isEnabled = document.body.classList.contains('high-contrast');
        sessionStorage.setItem('highContrastEnabled', isEnabled.toString());
        this.textContent = isEnabled ? 'ניגודיות רגילה' : 'ניגודיות גבוהה';
      });

      // Toggle negative contrast mode
      negativeContrastButton.addEventListener('click', function() {
        document.body.classList.toggle('negative-contrast');
        const isEnabled = document.body.classList.contains('negative-contrast');
        sessionStorage.setItem('negativeContrastEnabled', isEnabled.toString());
        this.textContent = isEnabled ? 'ניגודיות רגילה' : 'ניגודיות הפוכה';
      });

      // Toggle light background mode
      lightBackgroundButton.addEventListener('click', function() {
        document.body.classList.toggle('light-background');
        const isEnabled = document.body.classList.contains('light-background');
        sessionStorage.setItem('lightBackgroundEnabled', isEnabled.toString());
        this.textContent = isEnabled ? 'רקע רגיל' : 'רקע בהיר';
      });

      // Toggle highlight links mode
      highlightLinksButton.addEventListener('click', function() {
        document.body.classList.toggle('highlight-links');
        const isEnabled = document.body.classList.contains('highlight-links');
        sessionStorage.setItem('highlightLinksEnabled', isEnabled.toString());
        this.textContent = isEnabled ? 'קישורים רגילים' : 'הדגשת קישורים';
      });

      // Toggle readable font mode
      readableFontButton.addEventListener('click', function() {
        document.body.classList.toggle('readable-font');
        const isEnabled = document.body.classList.contains('readable-font');
        sessionStorage.setItem('readableFontEnabled', isEnabled.toString());
        this.textContent = isEnabled ? 'פונט רגיל' : 'פונט קריא';
      });

      // Add reset functionality
      resetButton.addEventListener('click', function() {
        // Reset all settings to default

        // Clear session storage entries related to accessibility
        sessionStorage.removeItem('accessibilityFontSize');
        sessionStorage.removeItem('grayscaleEnabled');
        sessionStorage.removeItem('highContrastEnabled');
        sessionStorage.removeItem('negativeContrastEnabled');
        sessionStorage.removeItem('lightBackgroundEnabled');
        sessionStorage.removeItem('highlightLinksEnabled');
        sessionStorage.removeItem('readableFontEnabled');

        // Reset font size
        currentFontSize = 1.0;
        document.body.style.fontSize = '100%';

        // Remove all accessibility classes from body
        document.body.classList.remove(
          'grayscale',
          'high-contrast',
          'negative-contrast',
          'light-background',
          'highlight-links',
          'readable-font'
        );

        // Reset all button texts to their default states
        grayscaleButton.textContent = 'גווני אפור';
        highContrastButton.textContent = 'ניגודיות גבוהה';
        negativeContrastButton.textContent = 'ניגודיות הפוכה';
        lightBackgroundButton.textContent = 'רקע בהיר';
        highlightLinksButton.textContent = 'הדגשת קישורים';
        readableFontButton.textContent = 'פונט קריא';

        // Show feedback that settings were reset
        // alert('כל אפשרויות הנגישות אופסו בהצלחה');
      });

      // Update button text to match current state
      if (document.body.classList.contains('grayscale')) {
        grayscaleButton.textContent = 'צבעים רגילים';
      }

      if (document.body.classList.contains('high-contrast')) {
        highContrastButton.textContent = 'ניגודיות רגילה';
      }

      if (document.body.classList.contains('negative-contrast')) {
        negativeContrastButton.textContent = 'ניגודיות רגילה';
      }

      if (document.body.classList.contains('light-background')) {
        lightBackgroundButton.textContent = 'רקע רגיל';
      }

      if (document.body.classList.contains('highlight-links')) {
        highlightLinksButton.textContent = 'קישורים רגילים';
      }

      if (document.body.classList.contains('readable-font')) {
        readableFontButton.textContent = 'פונט רגיל';
      }

      window.addEventListener('click', function(event) {
        if (event.target !== accessibilityModal &&
            event.target !== accessibilityWidget &&
            !accessibilityModal.contains(event.target)) {
          accessibilityModal.style.display = 'none';
        }
      });
    });
  </script>
  <!-- ...other scripts... -->
</body>
</html>
